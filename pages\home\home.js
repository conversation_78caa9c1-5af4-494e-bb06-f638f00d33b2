// pages/home/<USER>
Page({
  /**
   * 页面的初始数据
   */
  data: {
    userPoints: 128,
    hasCheckedIn: false,
    recentWorks: [
      {
        id: 1,
        image: 'https://images.unsplash.com/photo-1494790108755-2616c6d4e6e8?w=200&h=200&fit=crop',
        title: '优雅连衣裙',
        createTime: '2024-01-15'
      },
      {
        id: 2,
        image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=200&h=200&fit=crop',
        title: '职业套装',
        createTime: '2024-01-14'
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadUserData()
    this.checkDailyCheckin()
  },

  /**
   * 加载用户数据
   */
  loadUserData() {
    const app = getApp()
    this.setData({
      userPoints: app.getUserPoints()
    })
  },

  /**
   * 检查每日签到状态
   */
  checkDailyCheckin() {
    const today = new Date().toDateString()
    const lastCheckinDate = wx.getStorageSync('lastCheckinDate')
    
    this.setData({
      hasCheckedIn: lastCheckinDate === today
    })
  },

  /**
   * 每日签到
   */
  dailyCheckin() {
    if (this.data.hasCheckedIn) return

    const app = getApp()
    
    wx.showModal({
      title: '每日签到',
      content: '签到成功！获得5积分奖励',
      showCancel: false,
      success: () => {
        // 更新积分
        const newPoints = this.data.userPoints + 5
        app.updateUserPoints(newPoints)
        
        // 保存签到状态
        wx.setStorageSync('lastCheckinDate', new Date().toDateString())
        
        this.setData({
          userPoints: newPoints,
          hasCheckedIn: true
        })
        
        app.showToast('签到成功，获得5积分！', 'success')
      }
    })
  },

  /**
   * 开始换装
   */
  startDressup() {
    wx.switchTab({
      url: '/pages/dressup/dressup'
    })
  },

  /**
   * 跳转到积分中心
   */
  goToPoints() {
    wx.navigateTo({
      url: '/pages/points/points'
    })
  },

  /**
   * 跳转到历史记录
   */
  goToHistory() {
    wx.switchTab({
      url: '/pages/history/history'
    })
  },

  /**
   * 查看作品详情
   */
  viewWork(e) {
    const work = e.currentTarget.dataset.work
    wx.showModal({
      title: work.title,
      content: `创建时间：${work.createTime}`,
      confirmText: '查看详情',
      success: (res) => {
        if (res.confirm) {
          // 跳转到作品详情页面
          wx.navigateTo({
            url: `/pages/history/history?workId=${work.id}`
          })
        }
      }
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时更新用户积分
    this.loadUserData()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadUserData()
    this.checkDailyCheckin()
    
    // 模拟刷新数据
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '魔镜换装 - AI智能换装应用',
      path: '/pages/home/<USER>',
      imageUrl: 'https://images.unsplash.com/photo-1494790108755-2616c6d4e6e8?w=500&h=400&fit=crop'
    }
  }
})