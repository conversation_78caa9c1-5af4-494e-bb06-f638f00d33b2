<!--pages/history/history.wxml-->
<view class="container">
  <!-- 头部区域 -->
  <view class="header">
    <view class="header-title">换装历史</view>
    <text class="iconfont icon-search search-btn" bindtap="showSearch"></text>
  </view>
  
  <!-- 筛选标签 -->
  <view class="filter-tabs">
    <scroll-view class="tabs-scroll" scroll-x>
      <view class="tab-item {{activeTab === item.key ? 'active' : ''}}" 
            wx:for="{{filterTabs}}" 
            wx:key="key"
            bindtap="switchTab"
            data-tab="{{item.key}}">
        {{item.name}}
      </view>
    </scroll-view>
  </view>
  
  <!-- 统计信息 -->
  <view class="stats-card">
    <view class="stat-item">
      <view class="stat-number">{{totalCount}}</view>
      <view class="stat-label">总换装次数</view>
    </view>
    <view class="stat-item">
      <view class="stat-number">{{weekCount}}</view>
      <view class="stat-label">本周换装</view>
    </view>
    <view class="stat-item">
      <view class="stat-number">{{favoriteCount}}</view>
      <view class="stat-label">已收藏</view>
    </view>
  </view>
  
  <!-- 历史记录列表 -->
  <scroll-view class="history-list" scroll-y>
    <view class="history-item" 
          wx:for="{{filteredHistoryList}}" 
          wx:key="id"
          bindtap="viewDetail"
          data-item="{{item}}">
      <view class="history-image">
        <image src="{{item.resultImage}}" mode="aspectFill" />
      </view>
      <view class="history-content">
        <view class="history-header">
          <view class="history-info">
            <view class="history-title">{{item.title}}</view>
            <view class="history-time">{{item.createTime}}</view>
          </view>
          <text class="iconfont icon-star {{item.isFavorite ? 'favorited' : ''}}" 
                bindtap="toggleFavorite"
                data-id="{{item.id}}"
                catchtap="true"></text>
        </view>
        
        <view class="history-actions">
          <button class="action-btn share" 
                  bindtap="shareWork"
                  data-item="{{item}}"
                  catchtap="true">
            <text class="iconfont icon-share"></text>
            分享
          </button>
          <button class="action-btn edit" 
                  bindtap="editWork"
                  data-item="{{item}}"
                  catchtap="true">
            <text class="iconfont icon-edit"></text>
            重新编辑
          </button>
          <button class="action-btn save" 
                  bindtap="saveToAlbum"
                  data-item="{{item}}"
                  catchtap="true">
            <text class="iconfont icon-download"></text>
            保存
          </button>
        </view>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{filteredHistoryList.length === 0}}">
      <text class="iconfont icon-empty"></text>
      <view class="empty-title">暂无历史记录</view>
      <view class="empty-desc">快去体验AI换装功能吧</view>
      <button class="empty-action" bindtap="goToDressup">开始换装</button>
    </view>
    
    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore && filteredHistoryList.length > 0}}">
      <text class="loading-text">加载更多...</text>
    </view>
  </scroll-view>
</view>