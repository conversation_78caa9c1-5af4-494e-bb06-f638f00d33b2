/* pages/history/history.wxss */
.container {
  min-height: 100vh;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;
  background: linear-gradient(135deg, #1a0b2e 0%, #16213e 50%, #0f3460 100%);
}

/* 头部区域 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.header-title {
  color: white;
  font-size: 36rpx;
  font-weight: 600;
}

.search-btn {
  color: white;
  font-size: 40rpx;
}

/* 筛选标签 */
.filter-tabs {
  padding: 32rpx 32rpx 0;
}

.tabs-scroll {
  white-space: nowrap;
}

.tab-item {
  display: inline-block;
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 40rpx;
  padding: 16rpx 32rpx;
  color: white;
  font-size: 28rpx;
  margin-right: 24rpx;
  white-space: nowrap;
}

.tab-item.active {
  background: linear-gradient(45deg, #00d4ff, #ff006e);
  border-color: transparent;
}

/* 统计卡片 */
.stats-card {
  background: linear-gradient(45deg, rgba(139, 92, 246, 0.2), rgba(255, 0, 110, 0.2));
  border: 2rpx solid rgba(139, 92, 246, 0.3);
  border-radius: 20rpx;
  padding: 32rpx;
  margin: 32rpx;
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-number {
  color: white;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  color: #cccccc;
  font-size: 24rpx;
}

/* 历史记录列表 */
.history-list {
  flex: 1;
  padding: 0 32rpx 32rpx;
  height: calc(100vh - 400rpx);
  width: 100%;
  box-sizing: border-box;
}

/* 隐藏滚动条 */
.history-list::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}

.tabs-scroll::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}

.history-item {
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 30rpx;
  padding: 30rpx;
  margin-bottom: 32rpx;
  backdrop-filter: blur(20rpx);
  display: flex;
  gap: 32rpx;
  box-sizing: border-box;
  overflow: hidden;
}

.history-image {
  width: 160rpx;
  height: 192rpx;
  border-radius: 20rpx;
  overflow: hidden;
  flex-shrink: 0;
  box-shadow: 0 0 30rpx rgba(0, 212, 255, 0.2);
}

.history-image image {
  width: 100%;
  height: 100%;
}

.history-content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
}

.history-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.history-info {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.history-title {
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.history-time {
  color: #cccccc;
  font-size: 28rpx;
}

.icon-star {
  font-size: 32rpx;
  color: #888888;
}

.icon-star.favorited {
  color: #ffd700;
}

/* 操作按钮 */
.history-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  padding: 16rpx 20rpx;
  color: white;
  font-size: 22rpx;
  display: flex;
  align-items: center;
  gap: 6rpx;
  flex: 1;
  justify-content: center;
  min-width: 0;
  max-width: 160rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.action-btn.share {
  background: linear-gradient(45deg, #00d4ff, #ff006e);
  border: none;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 0;
}

.icon-empty {
  font-size: 120rpx;
  color: #666666;
  margin-bottom: 32rpx;
  display: block;
}

.empty-title {
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.empty-desc {
  color: #888888;
  font-size: 28rpx;
  margin-bottom: 48rpx;
}

.empty-action {
  background: linear-gradient(45deg, #00d4ff, #ff006e);
  border: none;
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
  color: white;
  font-size: 32rpx;
  font-weight: 600;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 32rpx 0;
}

.loading-text {
  color: #888888;
  font-size: 28rpx;
}

/* 图标字体 */
.icon-search::before { content: "🔍"; }
.icon-star::before { content: "⭐"; }
.icon-share::before { content: "📤"; }
.icon-edit::before { content: "✏️"; }
.icon-download::before { content: "💾"; }
.icon-empty::before { content: "📭"; }

/* 响应式适配 */
@media (max-width: 375px) {
  .history-item {
    padding: 24rpx;
    gap: 24rpx;
  }
  
  .history-image {
    width: 140rpx;
    height: 168rpx;
  }
  
  .action-btn {
    padding: 12rpx 16rpx;
    font-size: 20rpx;
    gap: 4rpx;
    max-width: 140rpx;
  }
  
  .history-title {
    font-size: 28rpx;
  }
  
  .history-time {
    font-size: 24rpx;
  }
}