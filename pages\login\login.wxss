/* pages/login/login.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a0b2e 0%, #16213e 50%, #0f3460 100%);
}

.content {
  padding: 64rpx;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Logo区域 */
.logo-section {
  text-align: center;
  margin-bottom: 96rpx;
}

.logo-icon {
  margin-bottom: 32rpx;
}

.icon-magic {
  font-size: 100rpx;
  color: #8b5cf6;
}

.logo-text {
  font-size: 56rpx;
  font-weight: bold;
  background: linear-gradient(45deg, #00d4ff, #ff006e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 16rpx;
}

.logo-desc {
  color: #cccccc;
  font-size: 28rpx;
}

/* 登录表单 */
.login-form {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.input-group {
  margin-bottom: 48rpx;
}

.code-input-wrapper {
  position: relative;
}

.code-input {
  padding-right: 200rpx !important;
}

.code-button {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #00d4ff;
  font-size: 28rpx;
  padding: 0;
  line-height: 1;
}

.code-button[disabled] {
  color: #666666;
}

.login-button {
  width: 100%;
  font-size: 36rpx;
  margin-bottom: 64rpx;
}

.login-button[disabled] {
  opacity: 0.5;
}

/* 分割线 */
.divider {
  display: flex;
  align-items: center;
  margin: 64rpx 0;
}

.divider-line {
  flex: 1;
  height: 2rpx;
  background-color: #666666;
}

.divider-text {
  padding: 0 32rpx;
  color: #888888;
  font-size: 28rpx;
}

/* 第三方登录 */
.social-login {
  margin-bottom: 64rpx;
}

.social-button {
  background: rgba(255, 255, 255, 0.1);
  border: 4rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 30rpx;
  padding: 24rpx;
  color: white;
  backdrop-filter: blur(20rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  width: 100%;
}

.social-button:active {
  border-color: #00d4ff;
  box-shadow: 0 0 30rpx rgba(0, 212, 255, 0.3);
}

.icon-wechat {
  color: #1aad19;
  font-size: 40rpx;
  margin-right: 24rpx;
}

/* 用户协议 */
.agreement {
  text-align: center;
  margin-top: 32rpx;
}

.agreement-text {
  color: #888888;
  font-size: 24rpx;
  line-height: 1.6;
}

.link {
  color: #00d4ff;
}