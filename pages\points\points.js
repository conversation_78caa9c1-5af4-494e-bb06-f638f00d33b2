// pages/points/points.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    userPoints: 128,
    rechargePackages: [
      {
        id: 1,
        points: 10,
        price: 6,
        unitPrice: '0.6',
        description: '基础套餐',
        popular: false
      },
      {
        id: 2,
        points: 50,
        price: 25,
        unitPrice: '0.5',
        description: '超值套餐',
        popular: true
      },
      {
        id: 3,
        points: 100,
        price: 45,
        unitPrice: '0.45',
        description: '豪华套餐',
        popular: false
      }
    ],
    consumeRecords: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadUserData()
    this.loadConsumeRecords()
  },

  /**
   * 加载用户数据
   */
  loadUserData() {
    const app = getApp()
    this.setData({
      userPoints: app.getUserPoints()
    })
  },

  /**
   * 加载消费记录
   */
  loadConsumeRecords() {
    const records = wx.getStorageSync('pointsRecords') || []
    
    // 模拟一些消费记录
    if (records.length === 0) {
      const mockRecords = [
        {
          id: 1,
          title: '基础换装',
          time: '2024-01-15 14:30',
          amount: 1,
          type: 'consume'
        },
        {
          id: 2,
          title: '观看广告',
          time: '2024-01-15 10:15',
          amount: 1,
          type: 'earn'
        },
        {
          id: 3,
          title: '充值',
          time: '2024-01-14 16:20',
          amount: 50,
          type: 'earn'
        }
      ]
      wx.setStorageSync('pointsRecords', mockRecords)
      this.setData({
        consumeRecords: mockRecords
      })
    } else {
      this.setData({
        consumeRecords: records
      })
    }
  },

  /**
   * 观看广告获取积分
   */
  watchAd() {
    const app = getApp()
    
    wx.showModal({
      title: '观看广告',
      content: '观看15秒广告视频即可获得1积分奖励',
      confirmText: '开始观看',
      success: (res) => {
        if (res.confirm) {
          this.simulateAdWatch()
        }
      }
    })
  },

  /**
   * 模拟观看广告
   */
  simulateAdWatch() {
    const app = getApp()
    
    app.showLoading('广告加载中...')
    
    // 模拟广告播放时间
    setTimeout(() => {
      app.hideLoading()
      
      wx.showModal({
        title: '广告观看完成',
        content: '恭喜您获得1积分奖励！',
        showCancel: false,
        success: () => {
          // 增加积分
          const newPoints = this.data.userPoints + 1
          app.updateUserPoints(newPoints)
          
          // 添加记录
          this.addPointsRecord({
            title: '观看广告',
            amount: 1,
            type: 'earn'
          })
          
          this.setData({
            userPoints: newPoints
          })
          
          app.showToast('获得1积分！', 'success')
        }
      })
    }, 2000)
  },

  /**
   * 购买积分套餐
   */
  purchasePackage(e) {
    const packageInfo = e.currentTarget.dataset.package
    
    wx.showModal({
      title: '确认购买',
      content: `购买${packageInfo.points}积分套餐，需要支付¥${packageInfo.price}元`,
      confirmText: '立即支付',
      success: (res) => {
        if (res.confirm) {
          this.processPayment(packageInfo)
        }
      }
    })
  },

  /**
   * 处理支付
   */
  processPayment(packageInfo) {
    const app = getApp()
    
    app.showLoading('处理支付中...')
    
    // 模拟支付过程
    setTimeout(() => {
      app.hideLoading()
      
      wx.showModal({
        title: '支付成功',
        content: `成功购买${packageInfo.points}积分！`,
        showCancel: false,
        success: () => {
          // 增加积分
          const newPoints = this.data.userPoints + packageInfo.points
          app.updateUserPoints(newPoints)
          
          // 添加记录
          this.addPointsRecord({
            title: '充值',
            amount: packageInfo.points,
            type: 'earn'
          })
          
          this.setData({
            userPoints: newPoints
          })
          
          app.showToast('充值成功！', 'success')
        }
      })
    }, 2000)
  },

  /**
   * 添加积分记录
   */
  addPointsRecord(record) {
    const records = this.data.consumeRecords
    const newRecord = {
      id: Date.now(),
      ...record,
      time: new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      }).replace(/\//g, '-')
    }
    
    records.unshift(newRecord)
    
    // 只保留最近50条记录
    if (records.length > 50) {
      records.splice(50)
    }
    
    this.setData({
      consumeRecords: records
    })
    
    // 保存到本地存储
    wx.setStorageSync('pointsRecords', records)
  },

  /**
   * 返回
   */
  goBack() {
    wx.navigateBack()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时刷新积分数据
    this.loadUserData()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadUserData()
    this.loadConsumeRecords()
    
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '魔镜换装积分中心 - 充值积分享受更多服务',
      path: '/pages/points/points'
    }
  }
})