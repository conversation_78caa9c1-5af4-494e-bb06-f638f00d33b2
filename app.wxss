/**app.wxss**/
page {
  background: linear-gradient(135deg, #1a0b2e 0%, #16213e 50%, #0f3460 100%);
  color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

/* 隐藏滚动条 */
::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}

/* 隐藏scroll-view滚动条 */
scroll-view::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}

/* 全局容器样式 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a0b2e 0%, #16213e 50%, #0f3460 100%);
}

/* 霓虹发光效果 */
.neon-glow {
  animation: neonGlow 2s ease-in-out infinite alternate;
}

@keyframes neonGlow {
  from {
    text-shadow: 0 0 10px #00d4ff, 0 0 20px #00d4ff, 0 0 30px #00d4ff;
  }
  to {
    text-shadow: 0 0 20px #ff006e, 0 0 30px #ff006e, 0 0 40px #ff006e;
  }
}

/* 霓虹按钮样式 */
.neon-button {
  background: linear-gradient(45deg, #00d4ff, #ff006e);
  border: none;
  border-radius: 50rpx;
  padding: 30rpx 60rpx;
  color: white;
  font-weight: bold;
  box-shadow: 0 0 40rpx rgba(0, 212, 255, 0.5);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.neon-button:active {
  box-shadow: 0 0 60rpx rgba(255, 0, 110, 0.7);
  transform: translateY(-4rpx);
}

/* 玻璃卡片效果 */
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 40rpx;
  backdrop-filter: blur(20rpx);
  padding: 40rpx;
}

/* 输入框样式 */
.neon-input {
  background: rgba(255, 255, 255, 0.1);
  border: 4rpx solid rgba(0, 212, 255, 0.3);
  border-radius: 30rpx;
  padding: 30rpx 40rpx;
  color: white;
  backdrop-filter: blur(20rpx);
}

.neon-input:focus {
  border-color: #00d4ff;
  box-shadow: 0 0 30rpx rgba(0, 212, 255, 0.5);
}

/* 渐变文字 */
.gradient-text {
  background: linear-gradient(45deg, #00d4ff, #ff006e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 积分徽章 */
.points-badge {
  background: linear-gradient(45deg, #ff006e, #8b5cf6);
  border-radius: 40rpx;
  padding: 16rpx 32rpx;
  color: white;
  font-weight: bold;
  box-shadow: 0 0 30rpx rgba(255, 0, 110, 0.3);
}

/* 功能卡片 */
.feature-card {
  background: rgba(255, 255, 255, 0.05);
  border: 2rpx solid rgba(0, 212, 255, 0.3);
  border-radius: 30rpx;
  padding: 30rpx;
  transition: all 0.3s ease;
}

.feature-card:active {
  border-color: #00d4ff;
  box-shadow: 0 0 30rpx rgba(0, 212, 255, 0.3);
}

/* 加载动画 */
.loading-ring {
  width: 160rpx;
  height: 160rpx;
  border: 8rpx solid transparent;
  border-top: 8rpx solid #00d4ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 粒子效果 */
.particle {
  position: absolute;
  width: 8rpx;
  height: 8rpx;
  background: #00d4ff;
  border-radius: 50%;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 1; }
  50% { transform: translateY(-40rpx) rotate(180deg); opacity: 0.5; }
}

/* 状态栏样式 */
.status-bar {
  height: 88rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40rpx;
  color: white;
  font-size: 28rpx;
  font-weight: 600;
}

/* 头部导航 */
.nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
}

.nav-title {
  color: white;
  font-size: 36rpx;
  font-weight: 600;
}

.nav-back {
  color: white;
  font-size: 40rpx;
}

/* 通用间距 */
.mt-xs { margin-top: 16rpx; }
.mt-sm { margin-top: 32rpx; }
.mt-md { margin-top: 48rpx; }
.mt-lg { margin-top: 64rpx; }
.mt-xl { margin-top: 96rpx; }

.mb-xs { margin-bottom: 16rpx; }
.mb-sm { margin-bottom: 32rpx; }
.mb-md { margin-bottom: 48rpx; }
.mb-lg { margin-bottom: 64rpx; }
.mb-xl { margin-bottom: 96rpx; }

.p-xs { padding: 16rpx; }
.p-sm { padding: 32rpx; }
.p-md { padding: 48rpx; }
.p-lg { padding: 64rpx; }

/* Flex布局 */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.flex-1 { flex: 1; }

/* 文字样式 */
.text-center { text-align: center; }
.text-white { color: #ffffff; }
.text-gray { color: #cccccc; }
.text-sm { font-size: 24rpx; }
.text-md { font-size: 28rpx; }
.text-lg { font-size: 32rpx; }
.text-xl { font-size: 36rpx; }
.text-2xl { font-size: 48rpx; }
.text-3xl { font-size: 64rpx; }

.font-bold { font-weight: bold; }
.font-semibold { font-weight: 600; }