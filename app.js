// app.js
App({
  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 登录
    wx.login({
      success: res => {
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
        console.log('登录成功', res.code)
      }
    })
  },

  onShow() {
    // 小程序启动，或从后台进入前台显示时触发
  },

  onHide() {
    // 小程序从前台进入后台时触发
  },

  globalData: {
    userInfo: null,
    userPoints: 128, // 用户积分
    isLogin: false, // 登录状态
    apiUrl: 'https://api.mojing.example.com' // API基础地址
  },

  // 全局方法：获取用户积分
  getUserPoints() {
    return this.globalData.userPoints
  },

  // 全局方法：更新用户积分
  updateUserPoints(points) {
    this.globalData.userPoints = points
    // 这里可以同步到服务器
  },

  // 全局方法：消费积分
  consumePoints(amount) {
    if (this.globalData.userPoints >= amount) {
      this.globalData.userPoints -= amount
      return true
    }
    return false
  },

  // 全局方法：显示toast
  showToast(title, icon = 'none') {
    wx.showToast({
      title: title,
      icon: icon,
      duration: 2000
    })
  },

  // 全局方法：显示loading
  showLoading(title = '加载中...') {
    wx.showLoading({
      title: title
    })
  },

  // 全局方法：隐藏loading
  hideLoading() {
    wx.hideLoading()
  }
})