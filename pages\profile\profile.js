// pages/profile/profile.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {},
    userStats: {
      dressupCount: 24,
      points: 128,
      favoriteCount: 5
    },
    defaultAvatar: 'https://images.unsplash.com/photo-1494790108755-2616c6d4e6e8?w=160&h=160&fit=crop'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadUserInfo()
    this.loadUserStats()
  },

  /**
   * 加载用户信息
   */
  loadUserInfo() {
    const app = getApp()
    
    // 从本地存储获取用户信息
    const userInfo = wx.getStorageSync('userInfo') || {}
    const userData = {
      ...userInfo,
      id: userInfo.id || '123456789',
      registerTime: userInfo.registerTime || '2024-01-01',
      isVip: userInfo.isVip || false
    }
    
    this.setData({
      userInfo: userData
    })
  },

  /**
   * 加载用户统计数据
   */
  loadUserStats() {
    const app = getApp()
    
    // 获取换装次数
    const historyList = wx.getStorageSync('dressupHistory') || []
    
    // 获取收藏数量
    const favoriteCount = historyList.filter(item => item.isFavorite).length
    
    this.setData({
      userStats: {
        dressupCount: historyList.length,
        points: app.getUserPoints(),
        favoriteCount: favoriteCount
      }
    })
  },

  /**
   * 编辑个人资料
   */
  editProfile() {
    wx.showModal({
      title: '编辑资料',
      content: '个人资料编辑功能开发中...',
      showCancel: false
    })
  },

  /**
   * 打开设置
   */
  openSettings() {
    wx.showActionSheet({
      itemList: ['清理缓存', '检查更新', '意见反馈'],
      success: (res) => {
        const app = getApp()
        switch (res.tapIndex) {
          case 0:
            this.clearCache()
            break
          case 1:
            app.showToast('已是最新版本', 'success')
            break
          case 2:
            this.showFeedback()
            break
        }
      }
    })
  },

  /**
   * 清理缓存
   */
  clearCache() {
    const app = getApp()
    
    wx.showModal({
      title: '清理缓存',
      content: '确认要清理应用缓存吗？这将删除临时文件但保留您的个人数据。',
      success: (res) => {
        if (res.confirm) {
          app.showLoading('清理中...')
          
          // 模拟清理过程
          setTimeout(() => {
            app.hideLoading()
            app.showToast('缓存清理完成', 'success')
          }, 2000)
        }
      }
    })
  },

  /**
   * 显示意见反馈
   */
  showFeedback() {
    wx.showModal({
      title: '意见反馈',
      content: '如有问题或建议，请通过客服联系我们。',
      confirmText: '联系客服',
      success: (res) => {
        if (res.confirm) {
          this.handleMenuTap({ currentTarget: { dataset: { action: 'customerService' } } })
        }
      }
    })
  },

  /**
   * 处理菜单点击
   */
  handleMenuTap(e) {
    const action = e.currentTarget.dataset.action
    
    switch (action) {
      case 'accountManage':
        this.accountManage()
        break
      case 'myFavorites':
        this.goToFavorites()
        break
      case 'pointsMall':
        this.pointsMall()
        break
      case 'inviteFriends':
        this.inviteFriends()
        break
      case 'customerService':
        this.customerService()
        break
      case 'aboutUs':
        this.aboutUs()
        break
    }
  },

  /**
   * 账户管理
   */
  accountManage() {
    wx.showActionSheet({
      itemList: ['修改昵称', '更换头像', '绑定手机', '安全设置'],
      success: (res) => {
        const actions = ['修改昵称', '更换头像', '绑定手机', '安全设置']
        wx.showModal({
          title: actions[res.tapIndex],
          content: '该功能开发中，敬请期待...',
          showCancel: false
        })
      }
    })
  },

  /**
   * 跳转到收藏页面
   */
  goToFavorites() {
    wx.switchTab({
      url: '/pages/history/history'
    })
  },

  /**
   * 跳转到历史页面
   */
  goToHistory() {
    wx.switchTab({
      url: '/pages/history/history'
    })
  },

  /**
   * 跳转到积分中心
   */
  goToPoints() {
    wx.navigateTo({
      url: '/pages/points/points'
    })
  },

  /**
   * 积分商城
   */
  pointsMall() {
    wx.showModal({
      title: '积分商城',
      content: '积分商城功能开发中，敬请期待...',
      showCancel: false
    })
  },

  /**
   * 邀请好友
   */
  inviteFriends() {
    wx.showModal({
      title: '邀请好友',
      content: '邀请好友使用魔镜换装，双方都可获得积分奖励！',
      confirmText: '立即邀请',
      success: (res) => {
        if (res.confirm) {
          wx.showShareMenu({
            withShareTicket: true,
            success: () => {
              const app = getApp()
              app.showToast('分享成功！', 'success')
            }
          })
        }
      }
    })
  },

  /**
   * 客服支持
   */
  customerService() {
    wx.showActionSheet({
      itemList: ['在线客服', '常见问题', '使用教程', '用户手册'],
      success: (res) => {
        const app = getApp()
        const services = ['在线客服', '常见问题', '使用教程', '用户手册']
        
        if (res.tapIndex === 0) {
          // 在线客服
          wx.showModal({
            title: '联系客服',
            content: '客服微信：mojing_kefu\n工作时间：9:00-18:00',
            confirmText: '复制微信号',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.setClipboardData({
                  data: 'mojing_kefu',
                  success: () => {
                    app.showToast('微信号已复制', 'success')
                  }
                })
              }
            }
          })
        } else {
          wx.showModal({
            title: services[res.tapIndex],
            content: '该功能开发中，敬请期待...',
            showCancel: false
          })
        }
      }
    })
  },

  /**
   * 关于我们
   */
  aboutUs() {
    wx.showModal({
      title: '关于魔镜换装',
      content: '版本：1.0.0\n\n魔镜换装是一款AI驱动的智能换装应用，为用户提供便捷的服装搭配预览体验。\n\n感谢您的使用！',
      confirmText: '好的',
      showCancel: false
    })
  },

  /**
   * 退出登录
   */
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          const app = getApp()
          
          // 清除登录状态
          wx.removeStorageSync('isLogin')
          wx.removeStorageSync('userInfo')
          app.globalData.isLogin = false
          app.globalData.userInfo = null
          
          app.showToast('已退出登录', 'success')
          
          // 跳转到登录页
          setTimeout(() => {
            wx.reLaunch({
              url: '/pages/login/login'
            })
          }, 1500)
        }
      }
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时刷新用户数据
    this.loadUserInfo()
    this.loadUserStats()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadUserInfo()
    this.loadUserStats()
    
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '魔镜换装 - AI智能换装应用',
      path: '/pages/splash/splash',
      imageUrl: this.data.defaultAvatar
    }
  }
})