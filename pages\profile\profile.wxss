/* pages/profile/profile.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a0b2e 0%, #16213e 50%, #0f3460 100%);
  display: flex;
  flex-direction: column;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 头部导航 */
.nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  padding-top: calc(32rpx + env(safe-area-inset-top));
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.nav-title {
  color: white;
  font-size: 36rpx;
  font-weight: 600;
}

.nav-settings {
  color: white;
  font-size: 40rpx;
}

/* 用户信息卡片 */
.profile-header {
  background: linear-gradient(135deg, #ff006e, #8b5cf6);
  border-radius: 40rpx;
  padding: 40rpx;
  margin: 24rpx 32rpx;
  box-shadow: 0 0 60rpx rgba(255, 0, 110, 0.4);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  border: 8rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 40rpx rgba(255, 255, 255, 0.2);
}

.user-details {
  flex: 1;
}

.user-name-section {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.user-name {
  color: white;
  font-size: 40rpx;
  font-weight: bold;
}

.vip-badge {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #000;
  padding: 8rpx 24rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.user-id {
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
  margin-bottom: 8rpx;
}

.register-time {
  color: rgba(255, 255, 255, 0.6);
  font-size: 24rpx;
}

.edit-btn {
  color: white;
  font-size: 36rpx;
}

/* 数据统计 */
.stats-section {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24rpx;
  padding: 0 32rpx;
  margin-bottom: 32rpx;
}

.stat-item {
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(0, 212, 255, 0.3);
  border-radius: 30rpx;
  padding: 40rpx;
  text-align: center;
  backdrop-filter: blur(20rpx);
}

.stat-number {
  color: white;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  color: #cccccc;
  font-size: 24rpx;
}

/* 功能菜单 */
.menu-section {
  flex: 1;
  padding: 0 32rpx;
  overflow-y: auto;
}

/* 隐藏滚动条 */
.menu-section::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}

.menu-item {
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 30rpx;
  padding: 24rpx 32rpx;
  margin-bottom: 24rpx;
  backdrop-filter: blur(20rpx);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.menu-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.menu-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.menu-icon.account {
  background: linear-gradient(45deg, #00d4ff, #0ea5e9);
}

.menu-icon.favorites {
  background: linear-gradient(45deg, #ffd700, #f59e0b);
}

.menu-icon.points {
  background: linear-gradient(45deg, #8b5cf6, #ec4899);
}

.menu-icon.invite {
  background: linear-gradient(45deg, #1aad19, #16a34a);
}

.menu-icon.service {
  background: linear-gradient(45deg, #6366f1, #8b5cf6);
}

.menu-icon.about {
  background: linear-gradient(45deg, #6b7280, #9ca3af);
}

.menu-icon text {
  color: white;
  font-size: 32rpx;
}

.menu-text {
  flex: 1;
}

.menu-title {
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.menu-desc {
  color: #cccccc;
  font-size: 28rpx;
}

.icon-arrow-right {
  color: #888888;
  font-size: 32rpx;
}

/* 退出登录 */
.logout-section {
  padding: 32rpx;
  margin-top: auto;
}

.logout-btn {
  width: 100%;
  background: rgba(255, 68, 68, 0.2);
  border: 2rpx solid rgba(255, 68, 68, 0.5);
  color: #ff4444;
  border-radius: 20rpx;
  padding: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  box-sizing: border-box;
}

/* 图标字体 */
.icon-settings::before { content: "⚙️"; }
.icon-edit::before { content: "✏️"; }
.icon-user::before { content: "👤"; }
.icon-heart::before { content: "❤️"; }
.icon-gift::before { content: "🎁"; }
.icon-users::before { content: "👥"; }
.icon-headset::before { content: "🎧"; }
.icon-info::before { content: "ℹ️"; }
.icon-arrow-right::before { content: "→"; }