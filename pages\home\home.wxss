/* pages/home/<USER>/
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a0b2e 0%, #16213e 50%, #0f3460 100%);
  padding: 32rpx;
  padding-bottom: 200rpx; /* 为底部导航留出空间 */
}

/* 头部区域 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 48rpx;
}

.header-left {
  flex: 1;
}

.app-title {
  color: white;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.app-desc {
  color: #cccccc;
  font-size: 28rpx;
}

/* 主要CTA按钮 */
.main-cta {
  background: linear-gradient(45deg, #00d4ff, #ff006e);
  border-radius: 60rpx;
  padding: 40rpx;
  margin-bottom: 48rpx;
  box-shadow: 0 0 60rpx rgba(0, 212, 255, 0.5);
}

.cta-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.cta-icon {
  font-size: 60rpx;
  color: white;
  margin-right: 32rpx;
}

.cta-text {
  flex: 1;
}

.cta-title {
  color: white;
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.cta-desc {
  color: rgba(255, 255, 255, 0.9);
  font-size: 28rpx;
}

/* 每日签到 */
.daily-checkin {
  margin-bottom: 48rpx;
}

.checkin-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.checkin-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.checkin-icon {
  width: 96rpx;
  height: 96rpx;
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.icon-gift {
  font-size: 36rpx;
  color: #000;
}

.checkin-text {
  flex: 1;
}

.checkin-title {
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.checkin-desc {
  color: #cccccc;
  font-size: 24rpx;
}

.checkin-button {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #000;
  border: none;
  border-radius: 50rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  font-weight: 600;
}

.checkin-button[disabled] {
  opacity: 0.5;
}

/* 功能卡片 */
.feature-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32rpx;
  margin-bottom: 48rpx;
}

.feature-card {
  background: rgba(255, 255, 255, 0.05);
  border: 2rpx solid rgba(0, 212, 255, 0.3);
  border-radius: 30rpx;
  padding: 30rpx;
  text-align: center;
}

.feature-icon {
  font-size: 48rpx;
  margin-bottom: 24rpx;
  display: block;
}

.feature-card:nth-child(1) .feature-icon {
  color: #00d4ff;
}

.feature-card:nth-child(2) .feature-icon {
  color: #ffd700;
}

.feature-title {
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.feature-desc {
  color: #888888;
  font-size: 24rpx;
}

/* 最近换装 */
.recent-works {
  padding: 40rpx;
}

.section-title {
  color: white;
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 32rpx;
}

.works-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24rpx;
}

.work-item {
  aspect-ratio: 1;
  border-radius: 20rpx;
  overflow: hidden;
  position: relative;
}

.work-image {
  width: 100%;
  height: 100%;
}

.add-work {
  background: linear-gradient(45deg, #00d4ff, #ff006e);
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-work .icon-plus {
  font-size: 48rpx;
  color: white;
}

/* 图标字体 */
.icon-magic::before { content: "✨"; }
.icon-coins::before { content: "🪙"; }
.icon-gift::before { content: "🎁"; }
.icon-history::before { content: "🕐"; }
.icon-plus::before { content: "➕"; }