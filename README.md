# 魔镜换装 - 微信小程序

## 📱 项目简介

魔镜换装是一款基于AI技术的智能换装小程序，为用户提供便捷的服装搭配预览体验。用户可以上传自拍照和服装图片，通过AI算法生成换装效果图。

## ✨ 主要功能

- **AI智能换装**: 上传自拍和服装图片，AI自动生成换装效果
- **多种换装模式**: 基础换装和高清换装两种选择
- **积分系统**: 观看广告获取免费积分，支持充值
- **换装历史**: 查看和管理历史换装记录
- **社交分享**: 一键分享换装作品到社交平台
- **用户中心**: 完善的个人信息管理

## 🛠 技术架构

- **框架**: 微信小程序原生框架
- **语言**: JavaScript ES6+
- **样式**: WXSS (支持CSS3特性)
- **布局**: WXML (类似HTML结构)
- **状态管理**: 小程序全局数据管理
- **存储**: 本地存储 + 云端同步

## 📁 项目结构

```
mini_program_project/
├── app.js                 # 小程序入口文件
├── app.json               # 全局配置
├── app.wxss              # 全局样式
├── project.config.json   # 项目配置
├── sitemap.json          # 站点地图配置
├── pages/                # 页面目录
│   ├── splash/           # 启动页
│   │   ├── splash.wxml
│   │   ├── splash.wxss
│   │   ├── splash.js
│   │   └── splash.json
│   ├── login/            # 登录页
│   ├── home/             # 首页
│   ├── dressup/          # 换装页
│   ├── points/           # 积分中心
│   ├── history/          # 换装历史
│   └── profile/          # 个人中心
└── README.md
```

## 🚀 开发环境设置

### 1. 安装微信开发者工具

1. 访问 [微信开发者工具官网](https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html)
2. 下载并安装适合您操作系统的版本
3. 使用微信扫码登录开发者工具

### 2. 导入项目

1. 打开微信开发者工具
2. 选择 "导入项目"
3. 选择项目目录：`mini_program_project`
4. 填写项目信息：
   - AppID: 使用测试号或申请正式AppID
   - 项目名称: 魔镜换装
   - 开发模式: 小程序

### 3. 项目配置

1. 在 `project.config.json` 中修改 `appid` 为您的小程序AppID
2. 根据需要调整其他配置项

## 🔧 本地开发

### 1. 启动开发服务器

1. 在微信开发者工具中打开项目
2. 点击 "编译" 按钮启动项目
3. 在模拟器中预览效果

### 2. 实时预览

开发者工具支持实时编译和热重载：
- 修改代码后自动重新编译
- 在模拟器中实时查看效果
- 支持多种设备尺寸预览

### 3. 调试功能

- **控制台**: 查看日志和错误信息
- **网络面板**: 监控网络请求
- **存储面板**: 查看本地存储数据
- **性能面板**: 分析页面性能

## 📱 真机调试

### 1. 预览功能

1. 点击开发者工具中的 "预览" 按钮
2. 使用微信扫描二维码
3. 在真机上体验完整功能

### 2. 真机调试

1. 点击开发者工具中的 "真机调试" 按钮
2. 扫码在真机上调试
3. 可以在开发者工具中查看真机的调试信息

## 🧪 测试

### 1. 单元测试

目前项目主要依赖手动测试，未来可以集成：
- Jest 测试框架
- 小程序测试工具

### 2. 功能测试清单

- [ ] 用户登录/注册流程
- [ ] AI换装核心功能
- [ ] 积分系统完整流程
- [ ] 历史记录管理
- [ ] 社交分享功能
- [ ] 个人中心操作

## 📦 发布部署

### 1. 代码上传

1. 在开发者工具中点击 "上传"
2. 填写版本号和项目备注
3. 上传代码到微信后台

### 2. 提交审核

1. 登录 [微信小程序后台](https://mp.weixin.qq.com/)
2. 进入 "开发" → "开发管理" → "开发版本"
3. 点击 "提交审核"
4. 填写审核信息并提交

### 3. 发布上线

1. 审核通过后会收到通知
2. 在后台点击 "发布" 按钮
3. 小程序正式上线

## 🔐 权限配置

项目需要以下小程序权限：

- `scope.userInfo`: 获取用户基本信息
- `scope.writePhotosAlbum`: 保存图片到相册
- `scope.camera`: 相机功能（换装时拍照）

在 `app.json` 中配置权限说明。

## 🌐 API集成

### 1. 后端接口

项目预留了后端API集成接口：
- 用户认证API
- AI换装处理API
- 积分系统API
- 文件上传API

### 2. 第三方服务

- 微信支付（充值功能）
- 内容安全检测
- 图片CDN服务
- 数据统计分析

## 📈 性能优化

### 1. 代码优化

- 使用按需加载减少包体积
- 图片懒加载和压缩
- 代码分包和预加载

### 2. 用户体验

- 添加加载动画和骨架屏
- 优化页面跳转动画
- 错误处理和重试机制

## 🐛 常见问题

### Q: 如何修改小程序AppID？
A: 在 `project.config.json` 文件中修改 `appid` 字段。

### Q: 真机预览时图片显示不正常？
A: 检查图片URL是否为HTTPS，并确保域名已在后台配置。

### Q: 如何添加新的页面？
A: 1. 在 `pages` 目录下创建新页面文件夹 2. 在 `app.json` 中添加页面路径

### Q: 样式在不同机型上显示异常？
A: 使用rpx单位适配不同屏幕，避免使用固定像素值。

## 📞 支持联系

- **开发团队**: 魔镜换装开发团队
- **技术支持**: <EMAIL>
- **项目地址**: [GitHub Repository]
- **文档更新**: 2024年1月

## 📄 许可证

本项目采用 MIT 许可证。详情请查看 [LICENSE](LICENSE) 文件。

---

**备注**: 这是一个演示项目，部分功能（如AI换装）需要集成真实的后端服务才能完整运行。