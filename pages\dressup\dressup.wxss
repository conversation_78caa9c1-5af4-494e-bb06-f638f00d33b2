/* pages/dressup/dressup.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a0b2e 0%, #16213e 50%, #0f3460 100%);
  overflow-x: hidden;
}

/* 头部导航 */
.nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.nav-title {
  color: white;
  font-size: 36rpx;
  font-weight: 600;
}

.nav-back {
  color: white;
  font-size: 40rpx;
}

/* 步骤指示器 */
.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 48rpx 24rpx;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.step-number {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: #666666;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.step-item.active .step-number {
  background: #00d4ff;
}

.step-text {
  color: #888888;
  font-size: 24rpx;
}

.step-item.active .step-text {
  color: white;
}

.step-line {
  width: 64rpx;
  height: 4rpx;
  background: #666666;
  margin: 0 16rpx;
}

.step-line.active {
  background: #00d4ff;
}

/* 内容区域 */
.content {
  flex: 1;
  padding: 0 24rpx 32rpx;
  height: calc(100vh - 280rpx);
  box-sizing: border-box;
}

/* 隐藏滚动条 */
.content::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}

/* 上传区域 */
.upload-section {
  margin-bottom: 48rpx;
}

.section-title {
  color: white;
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 32rpx;
}

.upload-area {
  background: rgba(255, 255, 255, 0.1);
  border: 4rpx dashed rgba(0, 212, 255, 0.5);
  border-radius: 40rpx;
  padding: 60rpx 20rpx;
  text-align: center;
  backdrop-filter: blur(20rpx);
  min-height: 300rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  margin: 0;
}

.upload-area.has-image {
  border-style: solid;
  border-color: #00d4ff;
  background: rgba(0, 212, 255, 0.1);
  padding: 0;
}

.upload-image {
  width: 100%;
  height: 300rpx;
  border-radius: 36rpx;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-icon {
  font-size: 80rpx;
  margin-bottom: 32rpx;
}

.upload-area:first-child .upload-icon {
  color: #00d4ff;
}

.upload-area:nth-child(2) .upload-icon {
  color: #ff006e;
}

.upload-text {
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.upload-desc {
  color: #cccccc;
  font-size: 28rpx;
}

/* 积分提示 */
.points-tip {
  background: linear-gradient(45deg, rgba(139, 92, 246, 0.2), rgba(255, 0, 110, 0.2));
  border: 2rpx solid rgba(139, 92, 246, 0.3);
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 48rpx;
  box-sizing: border-box;
}

.tip-content {
  display: flex;
  align-items: center;
}

.tip-icon {
  font-size: 36rpx;
  color: #ffd700;
  margin-right: 24rpx;
}

.tip-title {
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.tip-desc {
  color: #cccccc;
  font-size: 28rpx;
}

/* 处理按钮 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.process-button {
  border: none;
  border-radius: 50rpx;
  padding: 30rpx 60rpx;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.process-button.basic {
  background: linear-gradient(45deg, #00d4ff, #ff006e);
  box-shadow: 0 0 40rpx rgba(0, 212, 255, 0.5);
}

.process-button.premium {
  background: linear-gradient(45deg, #8b5cf6, #ec4899);
  box-shadow: 0 0 40rpx rgba(139, 92, 246, 0.5);
}

.process-button[disabled] {
  opacity: 0.5;
}

/* 处理中状态 */
.processing-state {
  text-align: center;
  padding: 64rpx 0;
}

.processing-title {
  color: white;
  font-size: 36rpx;
  font-weight: 600;
  margin: 32rpx 0 16rpx;
}

.processing-desc {
  color: #cccccc;
  font-size: 28rpx;
  margin-bottom: 48rpx;
}

.processing-progress {
  width: 100%;
  height: 8rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(45deg, #00d4ff, #ff006e);
  transition: width 0.3s ease;
}

/* 结果区域 */
.result-section {
  margin-top: 32rpx;
}

.result-image-container {
  margin-bottom: 48rpx;
}

.result-image {
  width: 100%;
  height: 600rpx;
  border-radius: 30rpx;
  box-shadow: 0 0 50rpx rgba(0, 212, 255, 0.4);
}

.result-actions {
  display: flex;
  gap: 32rpx;
  margin-bottom: 32rpx;
}

.action-btn {
  flex: 1;
  border: none;
  border-radius: 25rpx;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.action-btn.secondary {
  background: #666666;
  color: white;
}

.action-btn.primary {
  background: linear-gradient(45deg, #1aad19, #26d426);
  color: white;
}

.share-section {
  margin-top: 32rpx;
}

.share-btn {
  width: 100%;
  background: linear-gradient(45deg, #00d4ff, #ff006e);
  border: none;
  border-radius: 25rpx;
  padding: 24rpx;
  color: white;
  font-size: 28rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

/* 通用布局修复 */
page {
  overflow-x: hidden;
}

.container {
  box-sizing: border-box;
}

.nav-header {
  box-sizing: border-box;
}

.step-indicator {
  box-sizing: border-box;
}

.content {
  box-sizing: border-box;
}

/* 防止内容溢出 */
.upload-section,
.action-buttons,
.processing-state,
.result-section {
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

.section-title {
  box-sizing: border-box;
}

.upload-area {
  box-sizing: border-box;
}

.upload-placeholder {
  box-sizing: border-box;
}

.points-tip {
  box-sizing: border-box;
}

.tip-content {
  box-sizing: border-box;
}

.action-buttons {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.process-button {
  width: 100%;
  box-sizing: border-box;
}

.processing-state {
  box-sizing: border-box;
}

.processing-progress {
  box-sizing: border-box;
}

.progress-bar {
  box-sizing: border-box;
}

.result-section {
  box-sizing: border-box;
}

.result-image-container {
  box-sizing: border-box;
}

.result-actions {
  box-sizing: border-box;
}

.action-btn {
  box-sizing: border-box;
}

.share-section {
  box-sizing: border-box;
}

.share-btn {
  box-sizing: border-box;
}

/* 图标字体 */
.icon-back::before { content: "←"; }
.icon-camera::before { content: "📷"; }
.icon-tshirt::before { content: "👕"; }
.icon-coins::before { content: "🪙"; }
.icon-magic::before { content: "✨"; }
.icon-star::before { content: "⭐"; }
.icon-retry::before { content: "🔄"; }
.icon-save::before { content: "💾"; }
.icon-share::before { content: "📤"; }